# 学习伙伴 - 智能学习管理助手 高保真原型

## 项目概述

这是一个基于iOS设计规范的高保真原型，展示了"学习伙伴"应用的完整用户界面和交互流程。原型采用iPhone 15模拟器尺寸，完全符合iOS原生应用的视觉设计和交互体验。

## 功能模块

### 1. 课表管理模块
- **首页（课表视图）** - `index.html`
- **添加课程** - `add-course.html`
- **课程详情** - `course-detail.html`
- **编辑课程** - `edit-course.html`
- **课程笔记** - `course-notes.html`

### 2. 学习计划模块
- **学习计划** - `plan.html`
- **创建计划** - `create-plan.html`
- **计划详情** - `plan-detail.html`
- **编辑计划** - `edit-plan.html`
- **添加任务** - `add-task.html`
- **任务详情** - `task-detail.html`
- **计划模板** - `plan-templates.html`

### 3. 学习统计模块
- **学习统计** - `stats.html`
- **详细统计报告** - `stats-detail.html`

## 设计特色

### iOS原生设计规范
- ✅ 完整的iPhone 15状态栏和Home Indicator
- ✅ iOS风格的导航栏和标签栏
- ✅ 原生的表单控件和按钮样式
- ✅ 符合iOS的颜色系统和字体规范
- ✅ 流畅的过渡动画和交互反馈

### 清新风格设计
- 🎨 渐变背景和卡片式布局
- 🎨 柔和的色彩搭配
- 🎨 清晰的信息层级
- 🎨 优雅的图标和插图

### 完整的交互逻辑
- 🔗 所有页面间的跳转链接
- 🔗 表单验证和提交反馈
- 🔗 动态状态切换
- 🔗 响应式设计适配

## 页面导航结构

```
首页（课表视图）
├── 添加课程
├── 课程详情
│   ├── 编辑课程
│   └── 课程笔记
│
学习计划
├── 创建计划
├── 计划详情
│   ├── 编辑计划
│   ├── 添加任务
│   └── 任务详情
└── 计划模板
│
学习统计
└── 详细统计报告
```

## 使用说明

### 启动原型
1. 在浏览器中打开 `index.html` 开始体验
2. 建议使用Chrome或Safari浏览器获得最佳效果
3. 可以按F12打开开发者工具，选择iPhone 15设备模拟

### 主要交互流程

#### 课表管理流程
1. 从首页点击右上角"+"按钮添加课程
2. 填写课程信息并保存
3. 点击课程卡片查看详情
4. 可以编辑课程信息或添加笔记

#### 学习计划流程
1. 切换到"计划"标签页
2. 点击"创建新计划"或选择模板
3. 填写计划信息并保存
4. 在计划详情中添加具体任务
5. 跟踪任务完成进度

#### 学习统计流程
1. 切换到"统计"标签页查看概览
2. 点击"查看详细报告"获取更多数据
3. 可以切换不同时间段查看统计

### 响应式适配
- 在桌面浏览器中显示iPhone容器效果
- 在移动设备上自动适配全屏显示
- 支持触摸操作和手势交互

## 技术实现

### 前端技术栈
- **HTML5** - 语义化标签和结构
- **CSS3** - 现代样式和动画
- **JavaScript** - 交互逻辑和状态管理
- **Font Awesome** - 图标库
- **iOS设计系统** - 颜色、字体、组件规范

### 设计亮点
- 使用CSS Grid和Flexbox实现响应式布局
- 采用CSS变量管理设计系统
- 实现了iOS风格的毛玻璃效果
- 优化了触摸交互的反馈效果

## 文件结构

```
prototype/
├── README.md                 # 项目说明文档
├── base-template.html        # 基础模板文件
├── index.html               # 首页（课表视图）
├── add-course.html          # 添加课程
├── course-detail.html       # 课程详情
├── edit-course.html         # 编辑课程
├── course-notes.html        # 课程笔记
├── plan.html               # 学习计划
├── create-plan.html        # 创建计划
├── plan-detail.html        # 计划详情
├── edit-plan.html          # 编辑计划
├── add-task.html           # 添加任务
├── task-detail.html        # 任务详情
├── plan-templates.html     # 计划模板
├── stats.html              # 学习统计
└── stats-detail.html       # 详细统计报告
```

## 浏览器兼容性

- ✅ Chrome 80+
- ✅ Safari 13+
- ✅ Firefox 75+
- ✅ Edge 80+

## 注意事项

1. **最佳体验**：建议在移动设备或浏览器的设备模拟器中查看
2. **交互演示**：所有按钮和链接都有相应的跳转和反馈
3. **数据模拟**：当前使用静态数据进行演示
4. **功能完整性**：涵盖了需求文档中的所有核心功能

## 后续开发建议

1. **数据持久化**：集成本地存储或后端API
2. **状态管理**：使用Vue.js或React重构
3. **性能优化**：图片懒加载和代码分割
4. **测试覆盖**：添加单元测试和E2E测试

---

**开发团队**：学习伙伴产品团队  
**更新时间**：2024年3月  
**版本**：v1.0.0
