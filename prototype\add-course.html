<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 添加课程</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .form-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .form-group {
            border-bottom: 0.5px solid #e5e5e7;
            padding: 16px;
        }

        .form-group:last-child {
            border-bottom: none;
        }

        .form-label {
            font-size: 17px;
            font-weight: 400;
            color: #000;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            border: none;
            background: none;
            font-size: 17px;
            color: #000;
            outline: none;
            padding: 8px 0;
        }

        .form-input::placeholder {
            color: #c7c7cc;
        }

        .form-select {
            width: 100%;
            border: none;
            background: none;
            font-size: 17px;
            color: #000;
            outline: none;
            padding: 8px 0;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 32px;
        }

        .color-picker {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .color-option {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
        }

        .color-option.selected {
            border-color: #007AFF;
            transform: scale(1.1);
        }

        .color-option.blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .color-option.pink { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .color-option.cyan { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .color-option.green { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .color-option.orange { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .color-option.purple { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

        .time-row {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .time-input {
            flex: 1;
        }

        .save-button {
            background: #007AFF;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            margin: 16px;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
        }

        .save-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="index.html" class="nav-button">取消</a>
                <div class="nav-title">添加课程</div>
                <a href="index.html" class="nav-button">保存</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <form>
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">课程名称</label>
                            <input type="text" class="form-input" placeholder="请输入课程名称" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">教师姓名</label>
                            <input type="text" class="form-input" placeholder="请输入教师姓名">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">上课地点</label>
                            <input type="text" class="form-input" placeholder="请输入上课地点">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">学分</label>
                            <input type="number" class="form-input" placeholder="请输入学分" step="0.5" min="0">
                        </div>
                    </div>

                    <!-- 时间安排 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">星期</label>
                            <select class="form-select" required>
                                <option value="">请选择星期</option>
                                <option value="1">周一</option>
                                <option value="2">周二</option>
                                <option value="3">周三</option>
                                <option value="4">周四</option>
                                <option value="5">周五</option>
                                <option value="6">周六</option>
                                <option value="7">周日</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">上课时间</label>
                            <div class="time-row">
                                <input type="time" class="form-input time-input" required>
                                <span>至</span>
                                <input type="time" class="form-input time-input" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">上课周次</label>
                            <input type="text" class="form-input" placeholder="例如：1-16周" value="1-16周">
                        </div>
                    </div>

                    <!-- 课程设置 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">课程类别</label>
                            <select class="form-select">
                                <option value="">请选择类别</option>
                                <option value="必修课">必修课</option>
                                <option value="选修课">选修课</option>
                                <option value="实验课">实验课</option>
                                <option value="实习课">实习课</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">颜色主题</label>
                            <div class="color-picker">
                                <div class="color-option blue selected" data-color="blue"></div>
                                <div class="color-option pink" data-color="pink"></div>
                                <div class="color-option cyan" data-color="cyan"></div>
                                <div class="color-option green" data-color="green"></div>
                                <div class="color-option orange" data-color="orange"></div>
                                <div class="color-option purple" data-color="purple"></div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="save-button">保存课程</button>
                </form>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        // 颜色选择器
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 表单提交
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            // 这里可以添加保存逻辑
            alert('课程保存成功！');
            window.location.href = 'index.html';
        });
    </script>
</body>
</html>
