<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 添加任务</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .form-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .form-group {
            border-bottom: 0.5px solid #e5e5e7;
            padding: 16px;
        }

        .form-group:last-child {
            border-bottom: none;
        }

        .form-label {
            font-size: 17px;
            font-weight: 400;
            color: #000;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            border: none;
            background: none;
            font-size: 17px;
            color: #000;
            outline: none;
            padding: 8px 0;
        }

        .form-input::placeholder {
            color: #c7c7cc;
        }

        .form-textarea {
            width: 100%;
            border: none;
            background: none;
            font-size: 17px;
            color: #000;
            outline: none;
            padding: 8px 0;
            resize: none;
            min-height: 80px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .form-textarea::placeholder {
            color: #c7c7cc;
        }

        .form-select {
            width: 100%;
            border: none;
            background: none;
            font-size: 17px;
            color: #000;
            outline: none;
            padding: 8px 0;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 32px;
        }

        .priority-options {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .priority-option {
            flex: 1;
            padding: 12px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: #fff;
        }

        .priority-option.selected {
            border-color: #007AFF;
            background: #f0f8ff;
        }

        .priority-option.high {
            border-color: #FF3B30;
            color: #FF3B30;
        }

        .priority-option.high.selected {
            background: #fff5f5;
            border-color: #FF3B30;
        }

        .priority-option.medium {
            border-color: #FF9500;
            color: #FF9500;
        }

        .priority-option.medium.selected {
            background: #fff8f0;
            border-color: #FF9500;
        }

        .priority-option.low {
            border-color: #34C759;
            color: #34C759;
        }

        .priority-option.low.selected {
            background: #f0fff4;
            border-color: #34C759;
        }

        .difficulty-options {
            display: flex;
            gap: 12px;
            margin-top: 8px;
        }

        .difficulty-option {
            flex: 1;
            padding: 12px;
            border: 2px solid #e5e5e7;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: #fff;
            color: #666;
        }

        .difficulty-option.selected {
            border-color: #007AFF;
            background: #f0f8ff;
            color: #007AFF;
        }

        .time-row {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .time-input {
            flex: 1;
        }

        .save-button {
            background: #007AFF;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            margin: 16px;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
        }

        .save-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="plan-detail.html" class="nav-button">取消</a>
                <div class="nav-title">添加任务</div>
                <a href="plan-detail.html" class="nav-button">保存</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <form>
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">任务标题</label>
                            <input type="text" class="form-input" placeholder="请输入任务标题" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">任务描述</label>
                            <textarea class="form-textarea" placeholder="详细描述任务内容和要求..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">任务类型</label>
                            <select class="form-select" required>
                                <option value="">请选择任务类型</option>
                                <option value="study">学习</option>
                                <option value="assignment">作业</option>
                                <option value="exam">考试</option>
                                <option value="review">复习</option>
                            </select>
                        </div>
                    </div>

                    <!-- 时间设置 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">截止日期</label>
                            <input type="date" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">预估用时（小时）</label>
                            <input type="number" class="form-input" placeholder="请输入预估时长" min="0.5" step="0.5" required>
                        </div>
                    </div>

                    <!-- 任务设置 -->
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">优先级</label>
                            <div class="priority-options">
                                <div class="priority-option high" data-priority="high">
                                    <div style="font-weight: 600;">高</div>
                                    <div style="font-size: 12px;">紧急</div>
                                </div>
                                <div class="priority-option medium selected" data-priority="medium">
                                    <div style="font-weight: 600;">中</div>
                                    <div style="font-size: 12px;">正常</div>
                                </div>
                                <div class="priority-option low" data-priority="low">
                                    <div style="font-weight: 600;">低</div>
                                    <div style="font-size: 12px;">不急</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">难度等级</label>
                            <div class="difficulty-options">
                                <div class="difficulty-option" data-difficulty="easy">
                                    <div style="font-weight: 600;">简单</div>
                                    <div style="font-size: 12px;">Easy</div>
                                </div>
                                <div class="difficulty-option selected" data-difficulty="medium">
                                    <div style="font-weight: 600;">中等</div>
                                    <div style="font-size: 12px;">Medium</div>
                                </div>
                                <div class="difficulty-option" data-difficulty="hard">
                                    <div style="font-weight: 600;">困难</div>
                                    <div style="font-size: 12px;">Hard</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="save-button">添加任务</button>
                </form>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        // 优先级选择
        document.querySelectorAll('.priority-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.priority-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 难度选择
        document.querySelectorAll('.difficulty-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.difficulty-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 表单提交
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('任务添加成功！');
            window.location.href = 'plan-detail.html';
        });
    </script>
</body>
</html>
