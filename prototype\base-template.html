<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f2f2f7;
            overflow-x: hidden;
        }

        /* iPhone 15 Container */
        .phone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* Status Bar */
        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: relative;
            z-index: 100;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: flex-end;
        }

        .bar {
            width: 3px;
            background: #000;
            border-radius: 1px;
        }

        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        /* Navigation Bar */
        .navbar {
            height: 44px;
            background: #f2f2f7;
            border-bottom: 0.5px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            position: relative;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
        }

        .nav-button:hover {
            opacity: 0.7;
        }

        .nav-icon {
            font-size: 18px;
        }

        /* Content Area */
        .content {
            height: calc(100% - 54px - 44px - 83px);
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        /* Tab Bar */
        .tab-bar {
            height: 83px;
            background: #fff;
            border-top: 0.5px solid #e5e5e7;
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            padding-top: 8px;
            position: relative;
            z-index: 99;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            font-size: 10px;
            font-weight: 500;
            min-width: 60px;
        }

        .tab-item.active {
            color: #007AFF;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        /* Home Indicator */
        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        /* Common Styles */
        .section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
        }

        .section-header {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            font-size: 17px;
            font-weight: 600;
            color: #000;
        }

        .section-content {
            padding: 16px;
        }

        .list-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid #e5e5e7;
            text-decoration: none;
            color: #000;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .list-item:hover {
            background: #f2f2f7;
        }

        .list-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: #fff;
        }

        .list-content {
            flex: 1;
        }

        .list-title {
            font-size: 17px;
            font-weight: 400;
            color: #000;
            margin-bottom: 2px;
        }

        .list-subtitle {
            font-size: 15px;
            color: #8e8e93;
        }

        .list-arrow {
            color: #c7c7cc;
            font-size: 14px;
        }

        .button {
            background: #007AFF;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .button:hover {
            background: #0056b3;
        }

        .button-secondary {
            background: #f2f2f7;
            color: #007AFF;
        }

        .button-secondary:hover {
            background: #e5e5e7;
        }

        /* Responsive adjustments */
        @media (max-width: 430px) {
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="#" class="nav-button">
                    <i class="fas fa-chevron-left nav-icon"></i>
                </a>
                <div class="nav-title">学习伙伴</div>
                <a href="#" class="nav-button">
                    <i class="fas fa-plus nav-icon"></i>
                </a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- Page content goes here -->
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active">
                    <i class="tab-icon fas fa-calendar"></i>
                    <span>课表</span>
                </a>
                <a href="plan.html" class="tab-item">
                    <i class="tab-icon fas fa-tasks"></i>
                    <span>计划</span>
                </a>
                <a href="record.html" class="tab-item">
                    <i class="tab-icon fas fa-chart-line"></i>
                    <span>记录</span>
                </a>
                <a href="discover.html" class="tab-item">
                    <i class="tab-icon fas fa-compass"></i>
                    <span>发现</span>
                </a>
                <a href="profile.html" class="tab-item">
                    <i class="tab-icon fas fa-user"></i>
                    <span>我的</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
