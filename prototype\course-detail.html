<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 课程详情</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .course-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 24px 16px;
            color: #fff;
            text-align: center;
        }

        .course-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .course-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .course-time {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            display: inline-block;
        }

        .info-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #f2f2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #007AFF;
            font-size: 16px;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-size: 15px;
            color: #8e8e93;
            margin-bottom: 2px;
        }

        .info-value {
            font-size: 17px;
            color: #000;
            font-weight: 400;
        }

        .action-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .action-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            text-decoration: none;
            color: #000;
            transition: background-color 0.2s;
        }

        .action-item:last-child {
            border-bottom: none;
        }

        .action-item:hover {
            background: #f2f2f7;
        }

        .action-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: #fff;
        }

        .action-icon.edit { background: #007AFF; }
        .action-icon.notes { background: #FF9500; }

        .action-content {
            flex: 1;
        }

        .action-title {
            font-size: 17px;
            color: #000;
            margin-bottom: 2px;
        }

        .action-subtitle {
            font-size: 15px;
            color: #8e8e93;
        }

        .action-arrow {
            color: #c7c7cc;
            font-size: 14px;
        }

        .delete-button {
            background: #FF3B30;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            margin: 16px;
            transition: all 0.2s;
        }

        .delete-button:hover {
            background: #d70015;
            transform: translateY(-1px);
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="index.html" class="nav-button">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <div class="nav-title">课程详情</div>
                <a href="edit-course.html" class="nav-button">编辑</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 课程头部 -->
                <div class="course-header">
                    <div class="course-title">高等数学</div>
                    <div class="course-subtitle">张教授</div>
                    <div class="course-time">周一 08:00-09:30</div>
                </div>

                <!-- 课程信息 -->
                <div class="info-section">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">上课地点</div>
                            <div class="info-value">教学楼A-201</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">上课周次</div>
                            <div class="info-value">1-16周</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">学分</div>
                            <div class="info-value">4.0</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">课程类别</div>
                            <div class="info-value">必修课</div>
                        </div>
                    </div>
                </div>

                <!-- 操作选项 -->
                <div class="action-section">
                    <a href="edit-course.html" class="action-item">
                        <div class="action-icon edit">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">编辑课程</div>
                            <div class="action-subtitle">修改课程信息</div>
                        </div>
                        <i class="fas fa-chevron-right action-arrow"></i>
                    </a>
                    
                    <a href="course-notes.html" class="action-item">
                        <div class="action-icon notes">
                            <i class="fas fa-sticky-note"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">课程笔记</div>
                            <div class="action-subtitle">查看和编辑笔记</div>
                        </div>
                        <i class="fas fa-chevron-right action-arrow"></i>
                    </a>
                </div>

                <!-- 删除按钮 -->
                <button class="delete-button" onclick="deleteCourse()">删除课程</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        function deleteCourse() {
            if (confirm('确定要删除这门课程吗？此操作不可撤销。')) {
                alert('课程已删除');
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
