<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 课程笔记</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .course-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 16px;
            color: #fff;
            text-align: center;
        }

        .course-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .course-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .notes-editor {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            min-height: 400px;
        }

        .editor-toolbar {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid #e5e5e7;
            background: #f8f9fa;
        }

        .toolbar-button {
            background: none;
            border: none;
            color: #007AFF;
            font-size: 16px;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 8px;
            transition: background-color 0.2s;
        }

        .toolbar-button:hover {
            background: rgba(0,122,255,0.1);
        }

        .toolbar-button.active {
            background: #007AFF;
            color: #fff;
        }

        .notes-textarea {
            width: 100%;
            border: none;
            outline: none;
            padding: 16px;
            font-size: 16px;
            line-height: 1.6;
            color: #000;
            background: #fff;
            resize: none;
            min-height: 350px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .notes-textarea::placeholder {
            color: #c7c7cc;
        }

        .notes-info {
            background: #fff;
            margin: 0 16px 16px 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-size: 15px;
            color: #8e8e93;
        }

        .info-value {
            font-size: 15px;
            color: #000;
            font-weight: 500;
        }

        .save-button {
            background: #007AFF;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 16px 16px 16px;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
        }

        .save-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="course-detail.html" class="nav-button">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <div class="nav-title">课程笔记</div>
                <a href="#" class="nav-button" onclick="saveNotes()">保存</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 课程头部 -->
                <div class="course-header">
                    <div class="course-title">高等数学</div>
                    <div class="course-subtitle">课程笔记</div>
                </div>

                <!-- 笔记编辑器 -->
                <div class="notes-editor">
                    <div class="editor-toolbar">
                        <button class="toolbar-button" onclick="formatText('bold')" title="粗体">
                            <i class="fas fa-bold"></i>
                        </button>
                        <button class="toolbar-button" onclick="formatText('italic')" title="斜体">
                            <i class="fas fa-italic"></i>
                        </button>
                        <button class="toolbar-button" onclick="formatText('underline')" title="下划线">
                            <i class="fas fa-underline"></i>
                        </button>
                        <button class="toolbar-button" onclick="insertList()" title="列表">
                            <i class="fas fa-list"></i>
                        </button>
                        <button class="toolbar-button" onclick="clearFormat()" title="清除格式">
                            <i class="fas fa-eraser"></i>
                        </button>
                    </div>
                    
                    <textarea class="notes-textarea" placeholder="在这里记录课程笔记...

可以记录：
• 重要知识点
• 课堂讨论内容
• 作业要求
• 考试重点
• 个人思考

开始记录你的学习心得吧！">第一章：函数与极限

1. 函数的概念
   - 定义：设D是一个非空的数集，如果按照某个确定的对应关系f，使对于集合D中的任意一个数x，在数集I中都有唯一确定的数f(x)和它对应，那么就称f为从集合D到集合I的一个函数。

2. 极限的定义
   - ε-δ定义：对于函数f(x)，如果存在常数A，对于任意给定的正数ε，总存在正数δ，使得当0<|x-x₀|<δ时，有|f(x)-A|<ε，则称A为函数f(x)当x趋于x₀时的极限。

3. 重要定理
   - 夹逼定理
   - 单调有界定理

课堂重点：
✓ 理解函数的单调性和有界性
✓ 掌握极限的计算方法
✓ 熟练运用洛必达法则

作业：
- 课本第15页习题1-10
- 预习下一章内容</textarea>
                </div>

                <!-- 笔记信息 -->
                <div class="notes-info">
                    <div class="info-row">
                        <span class="info-label">字数统计</span>
                        <span class="info-value" id="word-count">245字</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">最后修改</span>
                        <span class="info-value">今天 14:30</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">创建时间</span>
                        <span class="info-value">3月18日 09:15</span>
                    </div>
                </div>

                <button class="save-button" onclick="saveNotes()">保存笔记</button>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        const textarea = document.querySelector('.notes-textarea');
        const wordCount = document.getElementById('word-count');

        // 更新字数统计
        function updateWordCount() {
            const text = textarea.value;
            const count = text.length;
            wordCount.textContent = count + '字';
        }

        // 监听文本变化
        textarea.addEventListener('input', updateWordCount);

        // 格式化文本（简单实现）
        function formatText(command) {
            // 这里可以实现更复杂的文本格式化
            console.log('Format:', command);
        }

        // 插入列表
        function insertList() {
            const cursorPos = textarea.selectionStart;
            const textBefore = textarea.value.substring(0, cursorPos);
            const textAfter = textarea.value.substring(cursorPos);
            textarea.value = textBefore + '\n• ' + textAfter;
            textarea.focus();
            textarea.setSelectionRange(cursorPos + 3, cursorPos + 3);
            updateWordCount();
        }

        // 清除格式
        function clearFormat() {
            console.log('Clear format');
        }

        // 保存笔记
        function saveNotes() {
            alert('笔记已保存！');
            // 这里可以添加实际的保存逻辑
        }

        // 初始化字数统计
        updateWordCount();
    </script>
</body>
</html>
