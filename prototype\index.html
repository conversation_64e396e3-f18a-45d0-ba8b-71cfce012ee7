<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 课表</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        /* 课表样式 */
        .schedule-header {
            background: #fff;
            padding: 16px;
            margin: 16px 16px 0 16px;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .week-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .week-nav {
            background: none;
            border: none;
            color: #007AFF;
            font-size: 18px;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
        }

        .week-info {
            text-align: center;
        }

        .week-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
        }

        .week-date {
            font-size: 13px;
            color: #8e8e93;
            margin-top: 2px;
        }

        .weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 8px;
        }

        .weekday {
            text-align: center;
            padding: 8px 4px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
        }

        .weekday.today {
            background: #007AFF;
            color: #fff;
        }

        .weekday:not(.today) {
            color: #8e8e93;
        }

        .schedule-grid {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .time-slot {
            display: flex;
            border-bottom: 0.5px solid #e5e5e7;
            min-height: 80px;
        }

        .time-slot:last-child {
            border-bottom: none;
        }

        .time-label {
            width: 60px;
            padding: 12px 8px;
            font-size: 12px;
            color: #8e8e93;
            text-align: center;
            border-right: 0.5px solid #e5e5e7;
            background: #f8f9fa;
        }

        .time-content {
            flex: 1;
            padding: 8px;
            position: relative;
        }

        .course-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            padding: 8px 12px;
            margin-bottom: 4px;
            color: #fff;
            text-decoration: none;
            display: block;
            transition: transform 0.2s;
        }

        .course-card:hover {
            transform: scale(1.02);
        }

        .course-card.math { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .course-card.english { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .course-card.physics { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .course-card.chemistry { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .course-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .course-info {
            font-size: 11px;
            opacity: 0.9;
        }

        .tab-bar {
            height: 83px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            padding-top: 8px;
            z-index: 99;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            font-size: 10px;
            font-weight: 500;
            min-width: 60px;
            padding: 4px 8px;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .tab-item.active {
            color: #007AFF;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <div class="nav-button" style="opacity: 0;">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="nav-title">课表</div>
                <a href="add-course.html" class="nav-button">
                    <i class="fas fa-plus"></i>
                </a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 课表头部 -->
                <div class="schedule-header">
                    <div class="week-selector">
                        <button class="week-nav">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="week-info">
                            <div class="week-title">第12周</div>
                            <div class="week-date">3月18日 - 3月24日</div>
                        </div>
                        <button class="week-nav">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="weekdays">
                        <div class="weekday">周一</div>
                        <div class="weekday">周二</div>
                        <div class="weekday today">周三</div>
                        <div class="weekday">周四</div>
                        <div class="weekday">周五</div>
                        <div class="weekday">周六</div>
                        <div class="weekday">周日</div>
                    </div>
                </div>

                <!-- 课表网格 -->
                <div class="schedule-grid">
                    <div class="time-slot">
                        <div class="time-label">08:00<br>09:30</div>
                        <div class="time-content">
                            <a href="course-detail.html?id=1" class="course-card math">
                                <div class="course-name">高等数学</div>
                                <div class="course-info">张教授 · 教学楼A-201</div>
                            </a>
                        </div>
                    </div>
                    
                    <div class="time-slot">
                        <div class="time-label">10:00<br>11:30</div>
                        <div class="time-content">
                            <a href="course-detail.html?id=2" class="course-card english">
                                <div class="course-name">大学英语</div>
                                <div class="course-info">李老师 · 教学楼B-305</div>
                            </a>
                        </div>
                    </div>
                    
                    <div class="time-slot">
                        <div class="time-label">14:00<br>15:30</div>
                        <div class="time-content">
                            <a href="course-detail.html?id=3" class="course-card physics">
                                <div class="course-name">大学物理</div>
                                <div class="course-info">王教授 · 实验楼C-102</div>
                            </a>
                        </div>
                    </div>
                    
                    <div class="time-slot">
                        <div class="time-label">16:00<br>17:30</div>
                        <div class="time-content">
                            <!-- 空时间段 -->
                        </div>
                    </div>
                    
                    <div class="time-slot">
                        <div class="time-label">19:00<br>20:30</div>
                        <div class="time-content">
                            <a href="course-detail.html?id=4" class="course-card chemistry">
                                <div class="course-name">有机化学</div>
                                <div class="course-info">赵老师 · 实验楼D-201</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item active">
                    <i class="tab-icon fas fa-calendar"></i>
                    <span>课表</span>
                </a>
                <a href="plan.html" class="tab-item">
                    <i class="tab-icon fas fa-tasks"></i>
                    <span>计划</span>
                </a>
                <a href="stats.html" class="tab-item">
                    <i class="tab-icon fas fa-chart-line"></i>
                    <span>统计</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
