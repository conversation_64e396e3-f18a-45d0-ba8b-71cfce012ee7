<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 计划详情</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .plan-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 24px 16px;
            color: #fff;
        }

        .plan-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .plan-meta {
            display: flex;
            gap: 16px;
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .plan-progress {
            background: rgba(255,255,255,0.2);
            padding: 16px;
            border-radius: 12px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .progress-value {
            font-size: 18px;
            font-weight: 600;
        }

        .progress-bar {
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-fill {
            height: 100%;
            background: #fff;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .section-header {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
        }

        .add-task-btn {
            color: #007AFF;
            font-size: 14px;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .add-task-btn:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .task-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            text-decoration: none;
            color: #000;
            transition: background-color 0.2s;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background: #f2f2f7;
        }

        .task-checkbox {
            width: 24px;
            height: 24px;
            border: 2px solid #c7c7cc;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .task-checkbox.completed {
            background: #34C759;
            border-color: #34C759;
            color: #fff;
        }

        .task-content {
            flex: 1;
        }

        .task-title {
            font-size: 16px;
            font-weight: 400;
            color: #000;
            margin-bottom: 4px;
        }

        .task-title.completed {
            text-decoration: line-through;
            color: #8e8e93;
        }

        .task-meta {
            font-size: 14px;
            color: #8e8e93;
            display: flex;
            gap: 12px;
        }

        .task-priority {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .priority-high { background: #FF3B30; }
        .priority-medium { background: #FF9500; }
        .priority-low { background: #34C759; }

        .action-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .action-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            text-decoration: none;
            color: #000;
            transition: background-color 0.2s;
        }

        .action-item:last-child {
            border-bottom: none;
        }

        .action-item:hover {
            background: #f2f2f7;
        }

        .action-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: #fff;
        }

        .action-icon.edit { background: #007AFF; }
        .action-icon.add { background: #34C759; }

        .action-content {
            flex: 1;
        }

        .action-title {
            font-size: 17px;
            color: #000;
            margin-bottom: 2px;
        }

        .action-subtitle {
            font-size: 15px;
            color: #8e8e93;
        }

        .action-arrow {
            color: #c7c7cc;
            font-size: 14px;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="plan.html" class="nav-button">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <div class="nav-title">计划详情</div>
                <a href="edit-plan.html" class="nav-button">编辑</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 计划头部 -->
                <div class="plan-header">
                    <div class="plan-title">期末考试复习计划</div>
                    <div class="plan-meta">
                        <span>考试计划</span>
                        <span>3月1日 - 4月15日</span>
                        <span>剩余12天</span>
                    </div>
                    
                    <div class="plan-progress">
                        <div class="progress-info">
                            <span class="progress-label">完成进度</span>
                            <span class="progress-value">65%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                        <div class="progress-stats">
                            <span>已完成 45小时</span>
                            <span>目标 80小时</span>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="section">
                    <div class="section-header">
                        <div class="section-title">学习任务</div>
                        <a href="add-task.html" class="add-task-btn">
                            <i class="fas fa-plus"></i> 添加
                        </a>
                    </div>
                    
                    <a href="task-detail.html?id=1" class="task-item">
                        <div class="task-checkbox completed">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="task-content">
                            <div class="task-title completed">复习高等数学第一章</div>
                            <div class="task-meta">
                                <span>3月18日截止</span>
                                <span>预计2小时</span>
                            </div>
                        </div>
                        <div class="task-priority priority-high"></div>
                    </a>
                    
                    <a href="task-detail.html?id=2" class="task-item">
                        <div class="task-checkbox">
                        </div>
                        <div class="task-content">
                            <div class="task-title">整理物理实验笔记</div>
                            <div class="task-meta">
                                <span>3月20日截止</span>
                                <span>预计3小时</span>
                            </div>
                        </div>
                        <div class="task-priority priority-medium"></div>
                    </a>
                    
                    <a href="task-detail.html?id=3" class="task-item">
                        <div class="task-checkbox">
                        </div>
                        <div class="task-content">
                            <div class="task-title">英语单词背诵</div>
                            <div class="task-meta">
                                <span>3月25日截止</span>
                                <span>预计1小时</span>
                            </div>
                        </div>
                        <div class="task-priority priority-low"></div>
                    </a>
                    
                    <a href="task-detail.html?id=4" class="task-item">
                        <div class="task-checkbox">
                        </div>
                        <div class="task-content">
                            <div class="task-title">化学方程式练习</div>
                            <div class="task-meta">
                                <span>4月1日截止</span>
                                <span>预计2小时</span>
                            </div>
                        </div>
                        <div class="task-priority priority-high"></div>
                    </a>
                </div>

                <!-- 操作选项 -->
                <div class="action-section">
                    <a href="edit-plan.html" class="action-item">
                        <div class="action-icon edit">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">编辑计划</div>
                            <div class="action-subtitle">修改计划信息和设置</div>
                        </div>
                        <i class="fas fa-chevron-right action-arrow"></i>
                    </a>
                    
                    <a href="add-task.html" class="action-item">
                        <div class="action-icon add">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="action-content">
                            <div class="action-title">添加任务</div>
                            <div class="action-subtitle">为计划添加新的学习任务</div>
                        </div>
                        <i class="fas fa-chevron-right action-arrow"></i>
                    </a>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        // 任务完成状态切换
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                this.classList.toggle('completed');
                const taskTitle = this.parentElement.querySelector('.task-title');
                taskTitle.classList.toggle('completed');
                
                if (this.classList.contains('completed')) {
                    this.innerHTML = '<i class="fas fa-check"></i>';
                } else {
                    this.innerHTML = '';
                }
            });
        });
    </script>
</body>
</html>
