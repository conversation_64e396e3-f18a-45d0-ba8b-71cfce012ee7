<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 计划模板</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .template-header {
            background: #fff;
            margin: 16px 16px 0 16px;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .template-title {
            font-size: 24px;
            font-weight: 700;
            color: #000;
            margin-bottom: 8px;
        }

        .template-subtitle {
            font-size: 16px;
            color: #8e8e93;
        }

        .category-section {
            margin: 16px;
        }

        .category-title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
            margin-bottom: 12px;
            padding: 0 4px;
        }

        .template-card {
            background: #fff;
            border-radius: 12px;
            margin-bottom: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #000;
            display: block;
            transition: transform 0.2s;
        }

        .template-card:hover {
            transform: translateY(-2px);
        }

        .template-content {
            padding: 16px;
        }

        .template-name {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            margin-bottom: 6px;
        }

        .template-description {
            font-size: 15px;
            color: #666;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .template-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #8e8e93;
        }

        .template-duration {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .template-difficulty {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .difficulty-easy {
            background: #E8F5E8;
            color: #4CAF50;
        }

        .difficulty-medium {
            background: #FFF3E0;
            color: #FF9500;
        }

        .difficulty-hard {
            background: #FFEBEE;
            color: #F44336;
        }

        .template-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #fff;
            margin-bottom: 12px;
        }

        .icon-exam { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .icon-skill { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .icon-language { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .icon-fitness { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .icon-reading { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .icon-project { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="plan.html" class="nav-button">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <div class="nav-title">计划模板</div>
                <div class="nav-button" style="opacity: 0;">
                    <i class="fas fa-plus"></i>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 模板头部 -->
                <div class="template-header">
                    <div class="template-title">选择模板</div>
                    <div class="template-subtitle">快速创建学习计划</div>
                </div>

                <!-- 考试准备类 -->
                <div class="category-section">
                    <div class="category-title">考试准备</div>
                    
                    <a href="create-plan.html?template=final-exam" class="template-card">
                        <div class="template-content">
                            <div class="template-icon icon-exam">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="template-name">期末考试复习计划</div>
                            <div class="template-description">系统性复习各科目，合理分配时间，提高考试成绩</div>
                            <div class="template-meta">
                                <div class="template-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>4-6周</span>
                                </div>
                                <div class="template-difficulty difficulty-medium">中等</div>
                            </div>
                        </div>
                    </a>
                    
                    <a href="create-plan.html?template=cet4" class="template-card">
                        <div class="template-content">
                            <div class="template-icon icon-language">
                                <i class="fas fa-language"></i>
                            </div>
                            <div class="template-name">英语四级备考</div>
                            <div class="template-description">听说读写全面提升，词汇语法重点突破</div>
                            <div class="template-meta">
                                <div class="template-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>3个月</span>
                                </div>
                                <div class="template-difficulty difficulty-hard">困难</div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- 技能提升类 -->
                <div class="category-section">
                    <div class="category-title">技能提升</div>
                    
                    <a href="create-plan.html?template=programming" class="template-card">
                        <div class="template-content">
                            <div class="template-icon icon-skill">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="template-name">编程技能提升</div>
                            <div class="template-description">从基础语法到项目实战，系统学习编程技能</div>
                            <div class="template-meta">
                                <div class="template-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>6个月</span>
                                </div>
                                <div class="template-difficulty difficulty-hard">困难</div>
                            </div>
                        </div>
                    </a>
                    
                    <a href="create-plan.html?template=reading" class="template-card">
                        <div class="template-content">
                            <div class="template-icon icon-reading">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="template-name">阅读习惯养成</div>
                            <div class="template-description">每日阅读，拓展知识面，提升理解能力</div>
                            <div class="template-meta">
                                <div class="template-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>持续进行</span>
                                </div>
                                <div class="template-difficulty difficulty-easy">简单</div>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- 项目实践类 -->
                <div class="category-section">
                    <div class="category-title">项目实践</div>
                    
                    <a href="create-plan.html?template=thesis" class="template-card">
                        <div class="template-content">
                            <div class="template-icon icon-project">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="template-name">毕业论文写作</div>
                            <div class="template-description">从选题到答辩，完整的论文写作流程规划</div>
                            <div class="template-meta">
                                <div class="template-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>4-6个月</span>
                                </div>
                                <div class="template-difficulty difficulty-hard">困难</div>
                            </div>
                        </div>
                    </a>
                    
                    <a href="create-plan.html?template=fitness" class="template-card">
                        <div class="template-content">
                            <div class="template-icon icon-fitness">
                                <i class="fas fa-dumbbell"></i>
                            </div>
                            <div class="template-name">健身计划</div>
                            <div class="template-description">科学健身，增强体质，保持健康的学习状态</div>
                            <div class="template-meta">
                                <div class="template-duration">
                                    <i class="fas fa-clock"></i>
                                    <span>3个月</span>
                                </div>
                                <div class="template-difficulty difficulty-medium">中等</div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
