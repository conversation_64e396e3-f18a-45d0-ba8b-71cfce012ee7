<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 学习计划</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .filter-section {
            background: #fff;
            margin: 16px 16px 0 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            gap: 8px;
        }

        .filter-tab {
            background: #f2f2f7;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: #8e8e93;
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-tab.active {
            background: #007AFF;
            color: #fff;
        }

        .plan-card {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #000;
            display: block;
            transition: transform 0.2s;
        }

        .plan-card:hover {
            transform: translateY(-2px);
        }

        .plan-header {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
        }

        .plan-title-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .plan-title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
            flex: 1;
        }

        .plan-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }

        .status-active {
            background: #E3F2FD;
            color: #2196F3;
        }

        .status-completed {
            background: #E8F5E8;
            color: #4CAF50;
        }

        .status-paused {
            background: #FFF3E0;
            color: #FF9500;
        }

        .plan-meta {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #8e8e93;
        }

        .plan-content {
            padding: 16px;
        }

        .plan-description {
            font-size: 15px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        .plan-progress {
            margin-bottom: 16px;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-label {
            font-size: 14px;
            color: #8e8e93;
        }

        .progress-value {
            font-size: 14px;
            font-weight: 600;
            color: #007AFF;
        }

        .progress-bar {
            height: 6px;
            background: #f2f2f7;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007AFF 0%, #00D4FF 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .plan-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #000;
        }

        .stat-label {
            font-size: 12px;
            color: #8e8e93;
            margin-top: 2px;
        }

        .plan-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .tag {
            background: #f2f2f7;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .add-plan-button {
            background: #007AFF;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            margin: 16px;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .add-plan-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
        }

        .tab-bar {
            height: 83px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            padding-top: 8px;
            z-index: 99;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            font-size: 10px;
            font-weight: 500;
            min-width: 60px;
            padding: 4px 8px;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .tab-item.active {
            color: #007AFF;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="plan-templates.html" class="nav-button">模板</a>
                <div class="nav-title">学习计划</div>
                <a href="create-plan.html" class="nav-button">
                    <i class="fas fa-plus"></i>
                </a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 筛选器 -->
                <div class="filter-section">
                    <div class="filter-tabs">
                        <button class="filter-tab active">全部</button>
                        <button class="filter-tab">进行中</button>
                        <button class="filter-tab">已完成</button>
                        <button class="filter-tab">已暂停</button>
                    </div>
                </div>

                <!-- 计划卡片 -->
                <a href="plan-detail.html?id=1" class="plan-card">
                    <div class="plan-header">
                        <div class="plan-title-row">
                            <div class="plan-title">期末考试复习计划</div>
                            <div class="plan-status status-active">进行中</div>
                        </div>
                        <div class="plan-meta">
                            <span>考试计划</span>
                            <span>3月1日 - 4月15日</span>
                        </div>
                    </div>
                    <div class="plan-content">
                        <div class="plan-description">为期末考试做全面复习准备，重点复习高等数学和大学物理，制定详细的复习时间表</div>
                        
                        <div class="plan-progress">
                            <div class="progress-info">
                                <span class="progress-label">完成进度</span>
                                <span class="progress-value">65%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 65%"></div>
                            </div>
                        </div>
                        
                        <div class="plan-stats">
                            <div class="stat-item">
                                <div class="stat-value">45h</div>
                                <div class="stat-label">已学习</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">80h</div>
                                <div class="stat-label">目标时长</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">12</div>
                                <div class="stat-label">剩余天数</div>
                            </div>
                        </div>
                        
                        <div class="plan-tags">
                            <span class="tag">考试</span>
                            <span class="tag">复习</span>
                            <span class="tag">重要</span>
                        </div>
                    </div>
                </a>

                <a href="plan-detail.html?id=2" class="plan-card">
                    <div class="plan-header">
                        <div class="plan-title-row">
                            <div class="plan-title">英语能力提升计划</div>
                            <div class="plan-status status-active">进行中</div>
                        </div>
                        <div class="plan-meta">
                            <span>技能提升</span>
                            <span>2月15日 - 6月15日</span>
                        </div>
                    </div>
                    <div class="plan-content">
                        <div class="plan-description">提高英语听说读写能力，准备四级考试，每天坚持学习</div>
                        
                        <div class="plan-progress">
                            <div class="progress-info">
                                <span class="progress-label">完成进度</span>
                                <span class="progress-value">29%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 29%"></div>
                            </div>
                        </div>
                        
                        <div class="plan-stats">
                            <div class="stat-item">
                                <div class="stat-value">35h</div>
                                <div class="stat-label">已学习</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">120h</div>
                                <div class="stat-label">目标时长</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">85</div>
                                <div class="stat-label">剩余天数</div>
                            </div>
                        </div>
                        
                        <div class="plan-tags">
                            <span class="tag">英语</span>
                            <span class="tag">四级</span>
                            <span class="tag">技能提升</span>
                        </div>
                    </div>
                </a>

                <a href="plan-detail.html?id=3" class="plan-card">
                    <div class="plan-header">
                        <div class="plan-title-row">
                            <div class="plan-title">化学实验技能训练</div>
                            <div class="plan-status status-paused">已暂停</div>
                        </div>
                        <div class="plan-meta">
                            <span>实践训练</span>
                            <span>3月10日 - 5月10日</span>
                        </div>
                    </div>
                    <div class="plan-content">
                        <div class="plan-description">掌握基本的化学实验操作技能，提高实验报告质量</div>
                        
                        <div class="plan-progress">
                            <div class="progress-info">
                                <span class="progress-label">完成进度</span>
                                <span class="progress-value">30%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 30%"></div>
                            </div>
                        </div>
                        
                        <div class="plan-stats">
                            <div class="stat-item">
                                <div class="stat-value">12h</div>
                                <div class="stat-label">已学习</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">40h</div>
                                <div class="stat-label">目标时长</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">51</div>
                                <div class="stat-label">剩余天数</div>
                            </div>
                        </div>
                        
                        <div class="plan-tags">
                            <span class="tag">化学</span>
                            <span class="tag">实验</span>
                            <span class="tag">技能</span>
                        </div>
                    </div>
                </a>

                <a href="create-plan.html" class="add-plan-button">
                    <i class="fas fa-plus" style="margin-right: 8px;"></i>
                    创建新计划
                </a>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <i class="tab-icon fas fa-calendar"></i>
                    <span>课表</span>
                </a>
                <a href="plan.html" class="tab-item active">
                    <i class="tab-icon fas fa-tasks"></i>
                    <span>计划</span>
                </a>
                <a href="stats.html" class="tab-item">
                    <i class="tab-icon fas fa-chart-line"></i>
                    <span>统计</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        // 筛选器功能
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 这里可以添加筛选逻辑
                console.log('筛选:', this.textContent);
            });
        });
    </script>
</body>
</html>
