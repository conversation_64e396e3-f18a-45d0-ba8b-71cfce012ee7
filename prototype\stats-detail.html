<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 详细统计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .period-selector {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .period-tabs {
            display: flex;
            gap: 8px;
        }

        .period-tab {
            background: #f2f2f7;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            color: #8e8e93;
            cursor: pointer;
            transition: all 0.2s;
            flex: 1;
            text-align: center;
        }

        .period-tab.active {
            background: #007AFF;
            color: #fff;
        }

        .stats-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .section-header {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            font-size: 18px;
            font-weight: 600;
            color: #000;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1px;
            background: #e5e5e7;
        }

        .stats-item {
            background: #fff;
            padding: 20px;
            text-align: center;
        }

        .stats-value {
            font-size: 24px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 4px;
        }

        .stats-label {
            font-size: 14px;
            color: #8e8e93;
        }

        .chart-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .chart-content {
            padding: 16px;
        }

        .chart-placeholder {
            height: 180px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8e8e93;
            font-size: 16px;
            position: relative;
            overflow: hidden;
        }

        .trend-chart {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: 100%;
            padding: 20px;
        }

        .trend-point {
            width: 8px;
            height: 8px;
            background: #007AFF;
            border-radius: 50%;
            position: relative;
        }

        .trend-point::before {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            background: #007AFF;
            opacity: 0.3;
        }

        .trend-point:nth-child(1) { margin-bottom: 40px; }
        .trend-point:nth-child(1)::before { height: 40px; }
        .trend-point:nth-child(2) { margin-bottom: 60px; }
        .trend-point:nth-child(2)::before { height: 60px; }
        .trend-point:nth-child(3) { margin-bottom: 30px; }
        .trend-point:nth-child(3)::before { height: 30px; }
        .trend-point:nth-child(4) { margin-bottom: 80px; }
        .trend-point:nth-child(4)::before { height: 80px; }
        .trend-point:nth-child(5) { margin-bottom: 50px; }
        .trend-point:nth-child(5)::before { height: 50px; }
        .trend-point:nth-child(6) { margin-bottom: 70px; }
        .trend-point:nth-child(6)::before { height: 70px; }
        .trend-point:nth-child(7) { margin-bottom: 90px; }
        .trend-point:nth-child(7)::before { height: 90px; }

        .achievement-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .achievement-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
        }

        .achievement-item:last-child {
            border-bottom: none;
        }

        .achievement-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: #fff;
        }

        .achievement-icon.gold { background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); }
        .achievement-icon.silver { background: linear-gradient(135deg, #C0C0C0 0%, #808080 100%); }
        .achievement-icon.bronze { background: linear-gradient(135deg, #CD7F32 0%, #8B4513 100%); }

        .achievement-content {
            flex: 1;
        }

        .achievement-title {
            font-size: 16px;
            font-weight: 600;
            color: #000;
            margin-bottom: 2px;
        }

        .achievement-desc {
            font-size: 14px;
            color: #8e8e93;
        }

        .achievement-date {
            font-size: 12px;
            color: #c7c7cc;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="stats.html" class="nav-button">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <div class="nav-title">详细统计</div>
                <a href="#" class="nav-button">导出</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 时间段选择器 -->
                <div class="period-selector">
                    <div class="period-tabs">
                        <button class="period-tab">本周</button>
                        <button class="period-tab active">本月</button>
                        <button class="period-tab">本学期</button>
                    </div>
                </div>

                <!-- 详细数据 -->
                <div class="stats-section">
                    <div class="section-header">学习数据总览</div>
                    <div class="stats-grid">
                        <div class="stats-item">
                            <div class="stats-value">125.5h</div>
                            <div class="stats-label">总学习时长</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-value">23</div>
                            <div class="stats-label">学习天数</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-value">5.5h</div>
                            <div class="stats-label">日均时长</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-value">89%</div>
                            <div class="stats-label">目标达成率</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-value">45</div>
                            <div class="stats-label">完成任务</div>
                        </div>
                        <div class="stats-item">
                            <div class="stats-value">12</div>
                            <div class="stats-label">连续天数</div>
                        </div>
                    </div>
                </div>

                <!-- 学习趋势图 -->
                <div class="chart-section">
                    <div class="section-header">学习趋势</div>
                    <div class="chart-content">
                        <div class="chart-placeholder">
                            <div class="trend-chart">
                                <div class="trend-point"></div>
                                <div class="trend-point"></div>
                                <div class="trend-point"></div>
                                <div class="trend-point"></div>
                                <div class="trend-point"></div>
                                <div class="trend-point"></div>
                                <div class="trend-point"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成就记录 -->
                <div class="achievement-section">
                    <div class="section-header">成就记录</div>
                    
                    <div class="achievement-item">
                        <div class="achievement-icon gold">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="achievement-content">
                            <div class="achievement-title">学习达人</div>
                            <div class="achievement-desc">连续学习30天</div>
                        </div>
                        <div class="achievement-date">3月15日</div>
                    </div>
                    
                    <div class="achievement-item">
                        <div class="achievement-icon silver">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div class="achievement-content">
                            <div class="achievement-title">时间管理师</div>
                            <div class="achievement-desc">完成100个学习任务</div>
                        </div>
                        <div class="achievement-date">3月10日</div>
                    </div>
                    
                    <div class="achievement-item">
                        <div class="achievement-icon bronze">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="achievement-content">
                            <div class="achievement-title">早起鸟</div>
                            <div class="achievement-desc">连续7天早上6点前开始学习</div>
                        </div>
                        <div class="achievement-date">3月5日</div>
                    </div>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        // 时间段选择器
        document.querySelectorAll('.period-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.period-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('selected');
                
                // 这里可以添加数据更新逻辑
                console.log('选择时间段:', this.textContent);
            });
        });
    </script>
</body>
</html>
