<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 学习统计</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .stats-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 24px 16px;
            color: #fff;
            text-align: center;
        }

        .stats-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .stats-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .overview-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .overview-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .overview-value {
            font-size: 24px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 4px;
        }

        .overview-label {
            font-size: 14px;
            color: #8e8e93;
        }

        .chart-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .chart-header {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
        }

        .chart-period {
            font-size: 14px;
            color: #8e8e93;
        }

        .chart-content {
            padding: 16px;
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8e8e93;
            font-size: 16px;
            position: relative;
            overflow: hidden;
        }

        .chart-bars {
            display: flex;
            align-items: flex-end;
            justify-content: space-around;
            height: 100%;
            padding: 20px;
        }

        .chart-bar {
            width: 20px;
            background: linear-gradient(to top, #007AFF, #00D4FF);
            border-radius: 4px 4px 0 0;
            position: relative;
        }

        .chart-bar:nth-child(1) { height: 60%; }
        .chart-bar:nth-child(2) { height: 80%; }
        .chart-bar:nth-child(3) { height: 45%; }
        .chart-bar:nth-child(4) { height: 90%; }
        .chart-bar:nth-child(5) { height: 70%; }
        .chart-bar:nth-child(6) { height: 55%; }
        .chart-bar:nth-child(7) { height: 85%; }

        .progress-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .progress-header {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
            font-size: 18px;
            font-weight: 600;
            color: #000;
        }

        .progress-item {
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
        }

        .progress-item:last-child {
            border-bottom: none;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-name {
            font-size: 16px;
            color: #000;
            font-weight: 500;
        }

        .progress-value {
            font-size: 14px;
            color: #007AFF;
            font-weight: 600;
        }

        .progress-bar {
            height: 6px;
            background: #f2f2f7;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-fill.math { background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); }
        .progress-fill.english { background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%); }
        .progress-fill.physics { background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%); }

        .detail-button {
            background: #007AFF;
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 16px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            margin: 16px;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .detail-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,122,255,0.4);
        }

        .tab-bar {
            height: 83px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-around;
            align-items: flex-start;
            padding-top: 8px;
            z-index: 99;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            font-size: 10px;
            font-weight: 500;
            min-width: 60px;
            padding: 4px 8px;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .tab-item.active {
            color: #007AFF;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <div class="nav-button" style="opacity: 0;">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="nav-title">学习统计</div>
                <a href="stats-detail.html" class="nav-button">详情</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 统计头部 -->
                <div class="stats-header">
                    <div class="stats-title">本周学习概览</div>
                    <div class="stats-subtitle">3月18日 - 3月24日</div>
                </div>

                <!-- 概览数据 -->
                <div class="overview-section">
                    <div class="overview-grid">
                        <div class="overview-item">
                            <div class="overview-value">28.5h</div>
                            <div class="overview-label">本周学习时长</div>
                        </div>
                        <div class="overview-item">
                            <div class="overview-value">85%</div>
                            <div class="overview-label">计划完成度</div>
                        </div>
                        <div class="overview-item">
                            <div class="overview-value">12</div>
                            <div class="overview-label">完成任务数</div>
                        </div>
                        <div class="overview-item">
                            <div class="overview-value">7</div>
                            <div class="overview-label">连续学习天数</div>
                        </div>
                    </div>
                </div>

                <!-- 学习时长图表 -->
                <div class="chart-section">
                    <div class="chart-header">
                        <div class="chart-title">每日学习时长</div>
                        <div class="chart-period">最近7天</div>
                    </div>
                    <div class="chart-content">
                        <div class="chart-placeholder">
                            <div class="chart-bars">
                                <div class="chart-bar"></div>
                                <div class="chart-bar"></div>
                                <div class="chart-bar"></div>
                                <div class="chart-bar"></div>
                                <div class="chart-bar"></div>
                                <div class="chart-bar"></div>
                                <div class="chart-bar"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 课程进度 -->
                <div class="progress-section">
                    <div class="progress-header">课程学习进度</div>
                    
                    <div class="progress-item">
                        <div class="progress-info">
                            <div class="progress-name">高等数学</div>
                            <div class="progress-value">75%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill math" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="progress-item">
                        <div class="progress-info">
                            <div class="progress-name">大学英语</div>
                            <div class="progress-value">60%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill english" style="width: 60%"></div>
                        </div>
                    </div>
                    
                    <div class="progress-item">
                        <div class="progress-info">
                            <div class="progress-name">大学物理</div>
                            <div class="progress-value">45%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill physics" style="width: 45%"></div>
                        </div>
                    </div>
                </div>

                <a href="stats-detail.html" class="detail-button">
                    <i class="fas fa-chart-bar" style="margin-right: 8px;"></i>
                    查看详细报告
                </a>
            </div>

            <!-- Tab Bar -->
            <div class="tab-bar">
                <a href="index.html" class="tab-item">
                    <i class="tab-icon fas fa-calendar"></i>
                    <span>课表</span>
                </a>
                <a href="plan.html" class="tab-item">
                    <i class="tab-icon fas fa-tasks"></i>
                    <span>计划</span>
                </a>
                <a href="stats.html" class="tab-item active">
                    <i class="tab-icon fas fa-chart-line"></i>
                    <span>统计</span>
                </a>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
