<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习伙伴 - 任务详情</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 393px;
            height: 852px;
            background: #000;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .status-bar {
            height: 54px;
            background: #f2f2f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            font-size: 17px;
            font-weight: 600;
            color: #000;
            z-index: 100;
        }

        .status-left { display: flex; align-items: center; gap: 6px; }
        .status-right { display: flex; align-items: center; gap: 6px; }
        .signal-bars { display: flex; gap: 2px; align-items: flex-end; }
        .bar { width: 3px; background: #000; border-radius: 1px; }
        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #34c759;
            border-radius: 1px;
        }

        .navbar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 99;
        }

        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: #000;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-button {
            color: #007AFF;
            font-size: 17px;
            text-decoration: none;
            font-weight: 400;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .nav-button:hover {
            background-color: rgba(0,122,255,0.1);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            background: #f2f2f7;
            position: relative;
        }

        .task-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 24px 16px;
            color: #fff;
        }

        .task-status {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .status-checkbox {
            width: 32px;
            height: 32px;
            border: 3px solid rgba(255,255,255,0.5);
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .status-checkbox.completed {
            background: #fff;
            border-color: #fff;
            color: #007AFF;
        }

        .status-text {
            font-size: 16px;
            opacity: 0.9;
        }

        .task-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .task-meta {
            display: flex;
            gap: 16px;
            font-size: 14px;
            opacity: 0.9;
        }

        .info-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 0.5px solid #e5e5e7;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #f2f2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #007AFF;
            font-size: 16px;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-size: 15px;
            color: #8e8e93;
            margin-bottom: 2px;
        }

        .info-value {
            font-size: 17px;
            color: #000;
            font-weight: 400;
        }

        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .priority-high {
            background: #FFEBEE;
            color: #F44336;
        }

        .priority-medium {
            background: #FFF3E0;
            color: #FF9500;
        }

        .priority-low {
            background: #E8F5E8;
            color: #4CAF50;
        }

        .description-section {
            background: #fff;
            margin: 16px;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .description-title {
            font-size: 18px;
            font-weight: 600;
            color: #000;
            margin-bottom: 12px;
        }

        .description-text {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
        }

        .action-buttons {
            padding: 16px;
            display: flex;
            gap: 12px;
        }

        .action-button {
            flex: 1;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            text-align: center;
            display: block;
        }

        .complete-button {
            background: #34C759;
            color: #fff;
            box-shadow: 0 2px 8px rgba(52,199,89,0.3);
        }

        .complete-button:hover {
            background: #28a745;
            transform: translateY(-1px);
        }

        .edit-button {
            background: #007AFF;
            color: #fff;
            box-shadow: 0 2px 8px rgba(0,122,255,0.3);
        }

        .edit-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
            opacity: 0.3;
        }

        @media (max-width: 430px) {
            body {
                padding: 0;
                background: #f2f2f7;
            }
            
            .phone-container {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
            }

            .screen {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span>9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <a href="plan-detail.html" class="nav-button">
                    <i class="fas fa-chevron-left"></i>
                </a>
                <div class="nav-title">任务详情</div>
                <a href="#" class="nav-button">编辑</a>
            </div>

            <!-- Content Area -->
            <div class="content">
                <!-- 任务头部 -->
                <div class="task-header">
                    <div class="task-status">
                        <div class="status-checkbox" onclick="toggleComplete()">
                            <!-- 空的，表示未完成 -->
                        </div>
                        <div class="status-text">点击标记为完成</div>
                    </div>
                    <div class="task-title">整理物理实验笔记</div>
                    <div class="task-meta">
                        <span>学习任务</span>
                        <span>3月20日截止</span>
                    </div>
                </div>

                <!-- 任务信息 -->
                <div class="info-section">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">截止日期</div>
                            <div class="info-value">2024年3月20日</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">预估用时</div>
                            <div class="info-value">3小时</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-flag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">优先级</div>
                            <div class="info-value">
                                <span class="priority-badge priority-medium">中等</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">难度等级</div>
                            <div class="info-value">中等</div>
                        </div>
                    </div>
                </div>

                <!-- 任务描述 -->
                <div class="description-section">
                    <div class="description-title">任务描述</div>
                    <div class="description-text">
                        整理本学期所有物理实验的笔记，包括实验原理、操作步骤、数据记录和结果分析。重点整理以下实验：
                        <br><br>
                        1. 单摆测重力加速度实验<br>
                        2. 牛顿环干涉实验<br>
                        3. 示波器的使用实验<br>
                        4. 电阻测量实验<br>
                        <br>
                        整理完成后需要制作实验报告总结，为期末考试做准备。
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-button complete-button" onclick="completeTask()">
                        <i class="fas fa-check" style="margin-right: 8px;"></i>
                        标记完成
                    </button>
                    <a href="#" class="action-button edit-button">
                        <i class="fas fa-edit" style="margin-right: 8px;"></i>
                        编辑任务
                    </a>
                </div>
            </div>

            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>

    <script>
        function toggleComplete() {
            const checkbox = document.querySelector('.status-checkbox');
            const statusText = document.querySelector('.status-text');
            
            if (checkbox.classList.contains('completed')) {
                checkbox.classList.remove('completed');
                checkbox.innerHTML = '';
                statusText.textContent = '点击标记为完成';
            } else {
                checkbox.classList.add('completed');
                checkbox.innerHTML = '<i class="fas fa-check"></i>';
                statusText.textContent = '任务已完成';
            }
        }

        function completeTask() {
            const checkbox = document.querySelector('.status-checkbox');
            const statusText = document.querySelector('.status-text');
            
            checkbox.classList.add('completed');
            checkbox.innerHTML = '<i class="fas fa-check"></i>';
            statusText.textContent = '任务已完成';
            
            alert('任务已标记为完成！');
        }
    </script>
</body>
</html>
