# 智学课表 - 智能课程管理与学习助手 需求文档

## 用户细分

### 主要用户群体
1. **在校学生**
   - 中小学生：需要管理课程安排，培养时间管理习惯
   - 高中生：管理复杂课表，追踪考试安排和作业截止时间
   - 大学生：管理选修课程，规划学期学习目标，社团活动安排

2. **职场学习者**
   - 在职人员：管理培训课程，平衡工作与学习时间
   - 技能提升者：跟踪线上课程进度，制定职业发展计划
   - 考证学习者：管理考试科目，制定复习计划

3. **教育工作者**
   - 教师：管理多班级课表，安排教学内容和考试
   - 培训师：跟踪学员进度，规划培训课程安排
   - 家长：协助孩子管理学习计划，监督学习进度

## 实际实现的页面层级结构

```
应用结构：
├─ L1: 首页（课表视图）
│  ├─ L2: 添加课程
│  └─ L2: 课程详情
│     ├─ L3: 编辑课程
│     └─ L3: 出勤记录
├─ L1: 学习计划
│  ├─ L2: 创建计划
│  ├─ L2: 计划详情
│  │  ├─ L3: 编辑计划
│  │  └─ L3: 添加任务
│  └─ L2: 计划模板
└─ L1: 学习统计
   └─ L2: 详细统计报告
```

### 层级属性说明表

| 页面名称 | 层级 | 上级页面 | 下级页面 | 访问路径 |
|---------|------|----------|----------|----------|
| 首页 | L1 | - | 添加课程, 课程详情, 日程视图 | /home |
| 添加课程 | L2 | 首页 | - | /home/<USER>
| 课程详情 | L2 | 首页 | 编辑课程, 课程笔记 | /home/<USER>
| 编辑课程 | L3 | 课程详情 | - | /home/<USER>/edit |
| 课程笔记 | L3 | 课程详情 | - | /home/<USER>/notes |
| 日程视图 | L2 | 首页 | - | /home/<USER>
| 学习计划 | L1 | - | 创建计划, 计划详情, 计划模板 | /plan |
| 创建计划 | L2 | 学习计划 | - | /plan/create |
| 计划详情 | L2 | 学习计划 | 编辑计划, 添加任务, 任务详情 | /plan/detail |
| 编辑计划 | L3 | 计划详情 | - | /plan/detail/edit |
| 添加任务 | L3 | 计划详情 | - | /plan/detail/add-task |
| 任务详情 | L3 | 计划详情 | - | /plan/detail/task |
| 计划模板 | L2 | 学习计划 | - | /plan/template |
| 学习统计 | L1 | - | 详细报告, 学习分析 | /stats |
| 详细报告 | L2 | 学习统计 | - | /stats/report |
| 学习分析 | L2 | 学习统计 | - | /stats/analysis |

## 核心功能

### 1. 智能课表管理
**UI交互流程：**
- 主界面显示周课表视图，支持左右滑动切换周次，上下滑动查看全天课程
- 点击右上角"+"按钮 → 进入添加课程页面
- 填写课程信息（课程名、教师、地点、时间、颜色标签） → 点击保存 → 返回主界面
- 长按课程卡片 → 弹出快捷操作菜单（编辑/删除/复制/分享）
- 点击课程卡片 → 进入课程详情页面，显示课程信息、签到记录、课程更新历史等
- 支持拖拽调整课程时间，智能冲突检测



### 2. 学习计划与目标
**UI交互流程：**
- 底部Tab点击"计划" → 进入计划管理页面
- 点击"创建计划"按钮 → 选择计划类型（学期计划/考试计划/技能提升）
- 设置计划名称、目标、时间范围、优先级 → 保存
- 点击计划卡片 → 进入计划详情，查看进度条和任务列表
- 添加子任务 → 设置任务类型、截止时间、重要程度
- 任务完成打卡 → 系统自动更新进度和统计数据

### 3. 学习数据分析
**UI交互流程：**
- 底部Tab点击"统计" → 进入数据分析页面
- 顶部切换时间维度（日/周/月/学期）
- 显示学习时长趋势图、课程出勤率、计划完成度
- 点击具体数据 → 查看详细分析报告
- 学习效率分析（最佳学习时段、专注度统计）
- 成就系统（连续打卡、目标达成、学习里程碑）



## 数据模型

### 课程实体 (Course)
```
struct Course {
    id: String              // 唯一标识符
    name: String           // 课程名称
    teacher: String        // 授课教师
    location: String       // 上课地点
    building: String       // 教学楼
    weekDay: Int          // 星期几 (1-7)
    startTime: Time       // 开始时间
    endTime: Time         // 结束时间
    weeks: [Int]          // 上课周次
    color: String         // 课程标识颜色
    category: String      // 课程类别
    credits: Float        // 学分
    notes: String         // 课程备注
    assignments: [Assignment] // 作业列表
    attendance: [Attendance]  // 出勤记录
    createdAt: Date       // 创建时间
    updatedAt: Date       // 更新时间
}
```



### 学习计划实体 (StudyPlan)
```
struct StudyPlan {
    id: String            // 唯一标识符
    title: String         // 计划标题
    description: String   // 计划描述
    type: String          // 计划类型 (semester/exam/skill)
    startDate: Date       // 开始日期
    endDate: Date         // 结束日期
    targetHours: Int      // 目标学习小时数
    currentHours: Int     // 已完成小时数
    tasks: [Task]         // 任务列表
    priority: String      // 优先级 (high/medium/low)
    status: String        // 状态 (active/completed/paused)
    progress: Float       // 完成进度 (0-1)
    tags: [String]        // 标签
}
```

### 任务实体 (Task)
```
struct Task {
    id: String            // 唯一标识符
    planId: String        // 关联计划ID
    title: String         // 任务标题
    description: String   // 任务描述
    type: String          // 任务类型 (study/assignment/exam)
    dueDate: Date         // 截止日期
    isCompleted: Bool     // 是否完成
    priority: String      // 优先级
    estimatedHours: Int   // 预估用时
    actualHours: Int      // 实际用时
    difficulty: String    // 难度等级
    tags: [String]        // 标签
    subtasks: [Subtask]   // 子任务
    completedAt: Date?    // 完成时间
}
```

### 学习记录实体 (StudyRecord)
```
struct StudyRecord {
    id: String            // 唯一标识符
    courseId: String?     // 关联课程ID（可选）
    planId: String?       // 关联计划ID（可选）
    taskId: String?       // 关联任务ID（可选）
    date: Date            // 学习日期
    startTime: Time       // 开始时间
    endTime: Time         // 结束时间
    duration: Int         // 学习时长（分钟）
    focusScore: Float     // 专注度评分 (0-10)
    notes: String         // 学习笔记
    mood: String          // 学习状态 (excellent/good/normal/tired)
    location: String      // 学习地点
    studyMethod: String   // 学习方式 (reading/practice/review)
}
```





## 应用信息

### 中文应用名称
**智学课表 - 智能学习管理助手**

### 英文应用名称
**SmartSchedule - Intelligent Study Planner**

### 应用商店描述（中文）
智学课表是一款专为学习者设计的智能课程管理应用。通过直观的课表视图、智能提醒系统和个性化学习计划，帮助用户高效管理学习时间，提升学习效率。支持课程安排、作业跟踪、学习统计和目标规划，让学习变得更有条理。内置学习社区功能，与同学分享学习心得，共同进步。无论是学生还是职场学习者，都能在这里找到适合的学习管理方案。

### 应用商店描述（英文）
SmartSchedule is an intelligent study management app designed for learners of all levels. With intuitive schedule views, smart reminder systems, and personalized study plans, it helps users efficiently manage their learning time and boost productivity. Features include course scheduling, assignment tracking, study analytics, and goal planning to make learning more organized. The built-in learning community allows users to share study insights and progress together. Whether you're a student or a professional learner, you'll find the perfect study management solution here.
